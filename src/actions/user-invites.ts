'use client';

import { API_ENDPOINTS } from '.';
import {
  UserInviteRequest,
  UserInviteResponse,
  UserInviteDetails,
  AcceptInviteRequest,
  AcceptInviteResponse,
} from '@/lib/types';

/**
 * Send an invitation to a user
 * @param inviteData The invitation data
 * @param token Bearer token for authorization
 * @returns Promise with the response
 */
export async function sendUserInvite(
  inviteData: UserInviteRequest,
  token: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: UserInviteResponse[];
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.USER_INVITES, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(inviteData),
    });

    const data = await response.json();

    return {
      ok: response.ok,
      status: response.status,
      data: response.ok ? data : undefined,
      error: !response.ok
        ? data.error.message || 'Failed to send invitation'
        : undefined,
    };
  } catch (error) {
    console.error('Error sending user invite:', error);
    return {
      ok: false,
      status: 500,
      error: 'Failed to send invitation. Please try again.',
    };
  }
}

/**
 * Get invitation details by token
 * @param inviteToken The invitation token
 * @param token Bearer token for authorization
 * @returns Promise with the invitation details
 */
export async function getUserInviteDetails(
  inviteToken: string,
  token: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: UserInviteDetails;
  error?: string;
}> {
  try {
    const url = new URL(API_ENDPOINTS.USER_INVITES);
    url.searchParams.set('token', inviteToken);

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    });

    const responseData = await response.json();
    console.log('Invite details response:', responseData);
    return {
      ok: response.ok,
      status: response.status,
      data: response.ok ? responseData : undefined,
      error: !response.ok
        ? responseData.error?.message || 'Failed to fetch invitation details'
        : undefined,
    };
  } catch (error) {
    console.error('Error fetching user invite details:', error);
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch invitation details. Please try again.',
    };
  }
}

/**
 * Accept a user invitation
 * @param acceptData The accept invitation data
 * @param token Bearer token for authorization
 * @returns Promise with the response
 */
export async function acceptUserInvite(
  acceptData: AcceptInviteRequest,
  token: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: AcceptInviteResponse;
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.USER_INVITES_ACCEPT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(acceptData),
    });

    const data = await response.json();

    return {
      ok: response.ok,
      status: response.status,
      data: response.ok ? data : undefined,
      error: !response.ok
        ? data.message || 'Failed to accept invitation'
        : undefined,
    };
  } catch (error) {
    console.error('Error accepting user invite:', error);
    return {
      ok: false,
      status: 500,
      error: 'Failed to accept invitation. Please try again.',
    };
  }
}
