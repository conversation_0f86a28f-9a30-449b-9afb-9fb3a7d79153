'use server';

import { CreateKnowledgeBaseInput, KnowledgeBaseType } from '@/lib/types';
import { API_ENDPOINTS, QueryParams } from '.';

export async function getKnowledgeBases(clinicId: string, token: string) {
  try {
    const url = new URL(API_ENDPOINTS.RETELL_KNOWLEDGE_BASES);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);

    const response = await fetch(url.toString(), {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      next: {
        revalidate: 6000,
        tags: [`knowledge-bases-${clinicId}`],
      },
    });

    const data = (await response.json()) as KnowledgeBaseType;

    return { ok: response.ok, status: response.status, data };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch knowledge bases. Please try again.',
    };
  }
}

export async function createKnowledgeBase(
  clinicId: string,
  token: string,
  data: CreateKnowledgeBaseInput,
) {
  try {
    const url = new URL(API_ENDPOINTS.RETELL_KNOWLEDGE_BASES);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);

    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const responseData = (await response.json()) as KnowledgeBaseType;

    return { ok: response.ok, status: response.status, data: responseData };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to create knowledge base. Please try again.',
    };
  }
}

export async function uploadKnowledgeBaseFiles(
  clinicId: string,
  token: string,
  files: FormData,
) {
  try {
    const url = new URL(API_ENDPOINTS.RETELL_KNOWLEDGE_BASES);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);

    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: files,
    });

    const responseData = (await response.json()) as KnowledgeBaseType;

    return { ok: response.ok, status: response.status, data: responseData };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to upload files to knowledge base. Please try again.',
    };
  }
}
