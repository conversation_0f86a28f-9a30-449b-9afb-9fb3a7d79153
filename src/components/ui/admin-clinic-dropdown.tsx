'use client';

import { useState } from 'react';
import { Check, ChevronsUpDown, Building2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Skeleton } from '@/components/ui/skeleton';
import { useAdminClinic } from '@/contexts/AdminClinicContext';

interface ClinicDropdownProps {
  className?: string;
}

export function AdminClinicDropdown({ className }: ClinicDropdownProps) {
  const [open, setOpen] = useState(false);
  const { clinics, selectedClinic, setSelectedClinic, loading } = useAdminClinic();

  console.log('AdminClinicDropdown render:', {
    clinicsCount: clinics.length,
    selectedClinic: selectedClinic?.clinic_name,
    loading,
  });

  if (loading) {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <Building2 className="h-4 w-4 text-muted-foreground" />
        <Skeleton className="h-8 w-48" />
      </div>
    );
  }

  if (clinics.length === 0) {
    return (
      <div
        className={cn(
          'flex items-center gap-2 text-muted-foreground',
          className,
        )}
      >
        <Building2 className="h-4 w-4" />
        <span className="text-sm">No clinics available</span>
      </div>
    );
  }

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <Building2 className="h-4 w-4 text-muted-foreground" />
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-[200px] justify-between"
          >
            {selectedClinic ? selectedClinic.clinic_name : 'Select clinic...'}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[200px] p-0">
          <Command>
            <CommandInput placeholder="Search clinics..." />
            <CommandList>
              <CommandEmpty>No clinic found.</CommandEmpty>
              <CommandGroup>
                {clinics.map((clinic) => (
                  <CommandItem
                  key={clinic._id}
                  value={clinic._id}
                  onSelect={(value) => {
                    const selected = clinics.find(c => c._id === value);
                    if (selected) {
                      setSelectedClinic(selected);
                      setOpen(false);
                    }
                  }}
                >
                
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        selectedClinic?._id === clinic._id
                          ? 'opacity-100'
                          : 'opacity-0',
                      )}
                    />
                    {clinic.clinic_name}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
