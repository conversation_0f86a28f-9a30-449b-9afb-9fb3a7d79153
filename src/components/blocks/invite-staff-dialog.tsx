'use client';

import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Check, Loader2, X } from 'lucide-react';
import { toast } from 'sonner';
import { fetchAuthSession } from 'aws-amplify/auth';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { sendUserInvite } from '@/actions/user-invites';
import { UserInviteRequest, ClinicType } from '@/lib/types';

const formSchema = z.object({
  name: z.string().min(1, { message: "Please enter the staff member's name" }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  role: z.enum(['staff', 'admin'], { message: 'Please select a role' }),
  clinicIds: z
    .array(z.string())
    .min(1, { message: 'Please select at least one clinic' }),
});

interface InviteStaffDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clinics?: ClinicType[];
}

export function InviteStaffDialog({
  open,
  onOpenChange,
  clinics = [],
}: InviteStaffDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  console.log('InviteStaffDialog - clinics prop:', clinics);
  console.log('InviteStaffDialog - clinics length:', clinics?.length || 0);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      role: 'staff' as const,
      clinicIds: [],
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    console.log('Form submission values:', values);
    console.log('Available clinics:', clinics);

    setIsSubmitting(true);

    try {
      const session = await fetchAuthSession();
      const token = session.tokens?.accessToken?.toString();

      if (!token) {
        toast.error('Authentication Error', {
          description: 'Please log in again to send invitations.',
        });
        setIsSubmitting(false);
        return;
      }

      const inviteData: UserInviteRequest = {
        invitee_email: values.email,
        invitee_name: values.name,
        role: values.role,
        clinic_ids: values.clinicIds,
      };

      const result = await sendUserInvite(inviteData, token);

      if (result.ok && result.data) {
        toast.success('Invitation Sent', {
          description: `An invitation has been sent to ${values.email}`,
        });

        setIsSuccess(true);

        setTimeout(() => {
          form.reset();
          setIsSuccess(false);
          onOpenChange(false);
        }, 3000);
      } else {
        console.error('API error:', result.error);
        toast.error(result.error || 'Failed to send');
      }
    } catch (error) {
      console.error('Error sending invitation:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to send invitation',
      );
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Invite Staff Member</DialogTitle>
          <DialogDescription>
            Send an invitation to a new staff member to join your clinic.
          </DialogDescription>
        </DialogHeader>

        {isSuccess ? (
          <Alert className="bg-emerald-500/10 border-emerald-200/40 backdrop-blur-md">
            <Check className="h-4 w-4 text-emerald-600" />
            <AlertTitle className="text-emerald-800 dark:text-emerald-300">
              Invitation Sent!
            </AlertTitle>
            <AlertDescription className="text-emerald-700 dark:text-emerald-400/90">
              An invitation has been sent to the email address. The staff member
              will receive instructions to join your clinic.
            </AlertDescription>
          </Alert>
        ) : (
          <>
            {(!clinics || clinics.length === 0) && (
              <Alert className="mb-4">
                <AlertDescription>
                  No clinics available. Please make sure you have access to at
                  least one clinic to send invitations.
                </AlertDescription>
              </Alert>
            )}
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input placeholder="John Doe" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a role" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="staff">Staff</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="clinicIds"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Clinics</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          const currentValues = field.value || [];
                          if (currentValues.includes(value)) {
                            field.onChange(
                              currentValues.filter((id) => id !== value),
                            );
                          } else {
                            field.onChange([...currentValues, value]);
                          }
                        }}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={
                                field.value?.length > 0
                                  ? `${field.value.length} clinic(s) selected`
                                  : 'Select clinics'
                              }
                            />
                          </SelectTrigger>
                        </FormControl>{' '}
                        <SelectContent>
                          {clinics && clinics.length > 0 ? (
                            clinics.map((clinic) => {
                              const isSelected =
                                field.value?.includes(clinic._id) || false;
                              return (
                                <SelectItem key={clinic._id} value={clinic._id}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>{clinic.clinic_name}</span>
                                    {isSelected && (
                                      <Check className="h-4 w-4 ml-2" />
                                    )}
                                  </div>
                                </SelectItem>
                              );
                            })
                          ) : (
                            <div className="px-2 py-1.5 text-sm text-gray-500">
                              No clinics available
                            </div>
                          )}
                        </SelectContent>
                      </Select>
                      {field.value?.length > 0 && (
                        <div className="mt-2">
                          <div className="flex flex-wrap gap-2">
                            {field.value.map((clinicId) => {
                              const clinic = clinics.find(
                                (c) => c._id === clinicId,
                              );
                              return clinic ? (
                                <div
                                  key={clinicId}
                                  className="inline-flex items-center px-2 py-1 bg-blue-300/20 dark:text-blue-200 text-blue-600 text-sm rounded-md"
                                >
                                  {clinic.clinic_name}
                                  <button
                                    type="button"
                                    onClick={() => {
                                      field.onChange(
                                        field.value.filter(
                                          (id) => id !== clinicId,
                                        ),
                                      );
                                    }}
                                    className="ml-1 text-blue-600 dark:text-blue-200 hover:text-blue-500"
                                    aria-label={`Remove ${clinic.clinic_name}`}
                                  >
                                    <X className="h-3 w-3" />
                                  </button>
                                </div>
                              ) : null;
                            })}
                          </div>
                        </div>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      'Send Invitation'
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
