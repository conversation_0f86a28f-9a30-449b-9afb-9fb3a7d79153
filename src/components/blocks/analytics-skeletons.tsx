import { BarChart3 } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../ui/card";
import { Skeleton } from "../ui/skeleton";

const MetricCardSkeleton = () => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <Skeleton className="h-4 w-24" />
      <Skeleton className="h-4 w-4" />
    </CardHeader>
    <CardContent>
      <Skeleton className="h-8 w-16 mb-1" />
      <Skeleton className="h-3 w-20" />
    </CardContent>
  </Card>
);

const ChartSkeleton = ({ height = 'h-72' }: { height?: string }) => (
  <div
    className={`${height} w-full animate-pulse bg-muted rounded-md flex items-center justify-center`}
  >
    <div className="text-center text-muted-foreground">
      <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p className="text-sm">Loading chart data...</p>
    </div>
  </div>
);

const EmptyState = ({
  title,
  description,
}: {
  title: string;
  description: string;
}) => (
  <div className="h-72 w-full flex items-center justify-center text-center">
    <div className="text-muted-foreground">
      <BarChart3 className="h-12 w-12 mx-auto mb-3 opacity-30" />
      <h3 className="text-lg font-medium mb-1">{title}</h3>
      <p className="text-sm">{description}</p>
    </div>
  </div>
);

export { MetricCardSkeleton, ChartSkeleton, EmptyState };
