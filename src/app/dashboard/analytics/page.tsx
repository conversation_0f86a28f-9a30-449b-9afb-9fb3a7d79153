'use client';

import { useState, useEffect, useMemo } from 'react';
import {
  CalendarIcon,
  Clock,
  Download,
  Phone,
  PhoneCall,
  PhoneMissed,
  RefreshCw,
  Check,
  ExternalLink,
  Play,
} from 'lucide-react';
import { format, subDays } from 'date-fns';

import { But<PERSON> } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Area<PERSON>hart,
  Area,
} from 'recharts';

// Import context and API functions
import { useClinic } from '@/contexts/ClinicContext';
import { useAuth } from '@/contexts/AuthContext';
import { fetchAnalyticsData } from '@/actions/analytics';
import type { ApiData, Call, ProcessedCallData } from '@/lib/types';
import { Badge } from '@/components/ui/badge';
import {
  ChartSkeleton,
  EmptyState,
  MetricCardSkeleton,
} from '@/components/blocks/analytics-skeletons';

// Time period type
type TimePeriod = 'day' | 'week' | 'month' | 'quarter' | 'year';

export default function CallAnalyticsPage() {
  const [date, setDate] = useState<Date>();
  const [timePeriod] = useState<TimePeriod>('month');
  const [analyticsData, setAnalyticsData] = useState<ApiData | null>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);

  const { selectedClinic } = useClinic();
  const { getTokens } = useAuth();

  const processCallsData = useMemo(() => {
    return (calls: Call[], period: 'day' | 'week' | 'month') => {
      if (!calls || calls.length === 0) {
        return [];
      }

      const now = new Date();
      const dataMap = new Map<string, ProcessedCallData>();
      const orderedKeys: string[] = [];

      if (period === 'day') {
        // Last 24 hours, grouped by hour
        for (let i = 23; i >= 0; i--) {
          const hour = new Date(now);
          hour.setHours(now.getHours() - i, 0, 0, 0);
          const key = hour.getHours().toString().padStart(2, '0') + ':00';
          orderedKeys.push(key);
          dataMap.set(key, {
            name: key,
            calls: 0,
            successful: 0,
            unsuccessful: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);
          if (isNaN(callDate.getTime())) return;

          const diffMs = now.getTime() - callDate.getTime();
          const diffHours = diffMs / (1000 * 60 * 60);

          if (diffHours >= 0 && diffHours < 24) {
            const hourKey =
              callDate.getHours().toString().padStart(2, '0') + ':00';
            const data = dataMap.get(hourKey);
            if (data) {
              data.calls += 1;
              data.successful += call.call_analysis?.call_successful ? 1 : 0;
              data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
            }
          }
        });
      } else if (period === 'week') {
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

        for (let i = 6; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(now.getDate() - i);
          date.setHours(0, 0, 0, 0);
          const key = `${date.getMonth() + 1}/${date.getDate()}`;
          orderedKeys.push(key);
          dataMap.set(key, {
            name: days[date.getDay()],
            calls: 0,
            successful: 0,
            unsuccessful: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);
          if (isNaN(callDate.getTime())) return;

          const callDateStart = new Date(callDate);
          callDateStart.setHours(0, 0, 0, 0);

          const nowStart = new Date(now);
          nowStart.setHours(0, 0, 0, 0);

          const diffMs = nowStart.getTime() - callDateStart.getTime();
          const diffDays = diffMs / (1000 * 60 * 60 * 24);

          if (diffDays >= 0 && diffDays < 7) {
            const dateKey = `${callDate.getMonth() + 1}/${callDate.getDate()}`;
            const data = dataMap.get(dateKey);
            if (data) {
              data.calls += 1;
              data.successful += call.call_analysis?.call_successful ? 1 : 0;
              data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
            }
          }
        });
      } else {
        // Monthly - last 30 days
        for (let i = 29; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(now.getDate() - i);
          date.setHours(0, 0, 0, 0);
          const key = `${date.getMonth() + 1}/${date.getDate()}`;
          orderedKeys.push(key);
          dataMap.set(key, {
            name: key,
            calls: 0,
            successful: 0,
            unsuccessful: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);
          if (isNaN(callDate.getTime())) return;

          const callDateStart = new Date(callDate);
          callDateStart.setHours(0, 0, 0, 0);

          const nowStart = new Date(now);
          nowStart.setHours(0, 0, 0, 0);

          const diffMs = nowStart.getTime() - callDateStart.getTime();
          const diffDays = diffMs / (1000 * 60 * 60 * 24);

          if (diffDays >= 0 && diffDays < 30) {
            const dateKey = `${callDate.getMonth() + 1}/${callDate.getDate()}`;
            const data = dataMap.get(dateKey);
            if (data) {
              data.calls += 1;
              data.successful += call.call_analysis?.call_successful ? 1 : 0;
              data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
            }
          }
        });
      }

      return orderedKeys.map((key) => dataMap.get(key)!);
    };
  }, []);

  const processHourlyData = useMemo(() => {
    return (calls: Call[]) => {
      if (!calls || calls.length === 0) return [];

      const hourlyMap = new Map<number, number>();

      // Initialize all hours
      for (let i = 0; i < 24; i++) {
        hourlyMap.set(i, 0);
      }

      calls.forEach((call) => {
        const callDate = new Date(call.start_timestamp);
        if (isNaN(callDate.getTime())) return;

        const hour = callDate.getHours();
        hourlyMap.set(hour, (hourlyMap.get(hour) || 0) + 1);
      });

      return Array.from(hourlyMap.entries())
        .map(([hour, calls]) => ({
          name:
            hour === 0
              ? '12 AM'
              : hour === 12
                ? '12 PM'
                : hour < 12
                  ? `${hour} AM`
                  : `${hour - 12} PM`,
          calls,
        }))
        .filter((_, index) => index >= 8 && index <= 19); // 8 AM to 7 PM
    };
  }, []);

  const processDurationData = useMemo(() => {
    return (calls: Call[]) => {
      if (!calls || calls.length === 0) return [];

      const durationCounts = {
        '< 1 min': 0,
        '1-3 min': 0,
        '3-5 min': 0,
        '5-10 min': 0,
        '> 10 min': 0,
      };

      calls.forEach((call) => {
        const durationMinutes = call.duration_ms / (1000 * 60);

        if (durationMinutes < 1) {
          durationCounts['< 1 min']++;
        } else if (durationMinutes < 3) {
          durationCounts['1-3 min']++;
        } else if (durationMinutes < 5) {
          durationCounts['3-5 min']++;
        } else if (durationMinutes < 10) {
          durationCounts['5-10 min']++;
        } else {
          durationCounts['> 10 min']++;
        }
      });

      return Object.entries(durationCounts).map(([name, value]) => ({
        name,
        value,
      }));
    };
  }, []);

  const processSentimentData = useMemo(() => {
    return (calls: Call[]) => {
      if (!calls || calls.length === 0) return [];

      const sentimentCounts = {
        Positive: 0,
        Neutral: 0,
        Negative: 0,
        Unknown: 0,
      };

      calls.forEach((call) => {
        const sentiment = call.call_analysis?.user_sentiment?.toLowerCase();
        if (sentiment === 'positive') {
          sentimentCounts['Positive']++;
        } else if (sentiment === 'negative') {
          sentimentCounts['Negative']++;
        } else if (sentiment === 'neutral') {
          sentimentCounts['Neutral']++;
        } else {
          sentimentCounts['Unknown']++;
        }
      });

      return Object.entries(sentimentCounts).map(([name, value]) => ({
        name,
        value,
        fill:
          name === 'Positive'
            ? '#10b981'
            : name === 'Negative'
              ? '#ef4444'
              : name === 'Neutral'
                ? '#f59e0b'
                : '#6b7280',
      }));
    };
  }, []);

  const processCallStatusData = useMemo(() => {
    return (calls: Call[]) => {
      if (!calls || calls.length === 0) return [];

      const statusCounts: Record<string, number> = {};

      calls.forEach((call) => {
        const status = call.call_status || 'Unknown';
        statusCounts[status] = (statusCounts[status] || 0) + 1;
      });

      const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

      return Object.entries(statusCounts).map(([name, value], index) => ({
        name,
        value,
        fill: colors[index % colors.length],
      }));
    };
  }, []);

  // Fetch analytics data
  useEffect(() => {
    const fetchData = async () => {
      if (!selectedClinic?._id) return;

      setAnalyticsLoading(true);
      try {
        const { accessToken } = await getTokens();
        if (!accessToken) {
          console.error('No access token available');
          setAnalyticsLoading(false);
          return;
        }

        // Calculate date range based on time period and selected date
        const endDate = date || new Date();
        let startDate = new Date(endDate);

        switch (timePeriod) {
          case 'day':
            startDate = subDays(endDate, 1);
            break;
          case 'week':
            startDate = subDays(endDate, 7);
            break;
          case 'month':
            startDate = subDays(endDate, 30);
            break;
          case 'quarter':
            startDate = subDays(endDate, 90);
            break;
          case 'year':
            startDate = subDays(endDate, 365);
            break;
        }

        const response = await fetchAnalyticsData(
          accessToken,
          selectedClinic._id,
          startDate.toISOString().split('T')[0],
          endDate.toISOString().split('T')[0],
        );

        if (response.ok && response.data) {
          setAnalyticsData(response.data);
        } else {
          console.error('Failed to fetch analytics data:', response.error);
        }
      } catch (error) {
        console.error('Error fetching analytics data:', error);
      } finally {
        setAnalyticsLoading(false);
      }
    };

    fetchData();
  }, [selectedClinic?._id, getTokens, date, timePeriod]);

  // Process data for charts
  const {
    dailyCallData,
    weeklyCallData,
    monthlyCallData,
    hourlyCallData,
    callDurationData,
    sentimentData,
    callStatusData,
    recentCalls,
  } = useMemo(() => {
    const calls = analyticsData?.calls || [];

    return {
      dailyCallData: processCallsData(calls, 'day'),
      weeklyCallData: processCallsData(calls, 'week'),
      monthlyCallData: processCallsData(calls, 'month'),
      hourlyCallData: processHourlyData(calls),
      callDurationData: processDurationData(calls),
      sentimentData: processSentimentData(calls),
      callStatusData: processCallStatusData(calls),
      recentCalls: calls.slice(0, 10).map((call, index) => ({
        id: index + 1,
        call_id: call.call_id,
        name: call.call_id.slice(5, 20) + '...',
        time: format(new Date(call.start_timestamp), 'h:mm a'),
        duration: `${Math.floor(call.duration_ms / 60000)}m ${Math.floor((call.duration_ms % 60000) / 1000)}s`,
        status: call.call_analysis?.call_successful ? 'answered' : 'missed',
        recording_url: call.recording_url,
        public_log_url: call.public_log_url,
        sentiment: call.call_analysis?.user_sentiment || 'Unknown',
      })),
    };
  }, [
    analyticsData,
    processCallsData,
    processHourlyData,
    processDurationData,
    processSentimentData,
    processCallStatusData,
  ]);

  const handleRefresh = () => {
    // Trigger a refresh by updating the dependency
    if (selectedClinic?._id) {
      setAnalyticsLoading(true);
      // The useEffect will handle the actual refresh
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Call Analytics</h1>
        <div className="mt-2 sm:mt-0 flex flex-wrap gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? format(date, 'PPP') : 'Pick a date'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={date}
                onSelect={setDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          {/* <Select
            value={timePeriod}
            onValueChange={(value: TimePeriod) => setTimePeriod(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Today</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
            </SelectContent>
          </Select> */}
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={analyticsLoading}
          >
            <RefreshCw
              className={`mr-2 h-4 w-4 ${analyticsLoading ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {analyticsLoading ? (
          <>
            <MetricCardSkeleton />
            <MetricCardSkeleton />
            <MetricCardSkeleton />
            <MetricCardSkeleton />
          </>
        ) : (
          <>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Calls
                </CardTitle>
                <Phone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl md:text-3xl font-bold">
                  {analyticsData?.calls?.length || 0}
                </div>
                <p className="text-sm text-cyan-600 dark:text-cyan-400 mt-2">
                  {timePeriod === 'day'
                    ? 'Last 24 hours'
                    : timePeriod === 'week'
                      ? 'Last 7 days'
                      : timePeriod === 'month'
                        ? 'Last 30 days'
                        : timePeriod === 'quarter'
                          ? 'Last 90 days'
                          : 'Last year'}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Answered Calls
                </CardTitle>
                <PhoneCall className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl md:text-3xl font-bold">
                  {analyticsData?.calls?.filter(
                    (call) => call.call_analysis?.call_successful,
                  ).length || 0}
                </div>
                <p className="text-sm text-green-600 dark:text-green-400 mt-2">
                  {analyticsData?.calls && analyticsData.calls.length > 0
                    ? `${Math.round(
                        (analyticsData.calls.filter(
                          (call) => call.call_analysis?.call_successful,
                        ).length /
                          analyticsData.calls.length) *
                          100,
                      )}% success rate`
                    : 'No data available'}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Missed Calls
                </CardTitle>
                <PhoneMissed className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl md:text-3xl font-bold">
                  {analyticsData?.calls?.filter(
                    (call) => !call.call_analysis?.call_successful,
                  ).length || 0}
                </div>
                <p className="text-sm text-red-600 dark:text-red-400 mt-2">
                  {analyticsData?.calls && analyticsData.calls.length > 0
                    ? `${Math.round(
                        (analyticsData.calls.filter(
                          (call) => !call.call_analysis?.call_successful,
                        ).length /
                          analyticsData.calls.length) *
                          100,
                      )}% missed rate`
                    : 'No data available'}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Avg. Call Duration
                </CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl md:text-3xl font-bold">
                  {analyticsData?.summary
                    ? `${analyticsData.summary.average_duration_in_minutes.toFixed(1)}m`
                    : '0m'}
                </div>
                <p className="text-sm mt-2 text-muted-foreground">
                  Average duration
                </p>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      <Tabs defaultValue="daily" className="space-y-4">
        <TabsList>
          <TabsTrigger value="daily">Daily</TabsTrigger>
          <TabsTrigger value="weekly">Weekly</TabsTrigger>
          <TabsTrigger value="monthly">Monthly</TabsTrigger>
        </TabsList>
        <TabsContent value="daily" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
            <Card className="lg:col-span-4">
              <CardHeader>
                <CardTitle>Daily Call Volume</CardTitle>
                <CardDescription>
                  Number of calls per day this week
                </CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                {analyticsLoading ? (
                  <ChartSkeleton height="h-[400px]" />
                ) : dailyCallData.every((data) => data.calls === 0) ? (
                  <EmptyState
                    title="No daily data"
                    description="No call data available for the selected period."
                  />
                ) : (
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={dailyCallData}>
                      <CartesianGrid
                        strokeDasharray="3 3"
                        stroke="var(--muted)"
                      />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar
                        dataKey="successful"
                        name="Successful Calls"
                        fill="#10b981"
                        radius={[4, 4, 0, 0]}
                      />
                      <Bar
                        dataKey="unsuccessful"
                        name="Unsuccessful Calls"
                        fill="#ef4444"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>User Sentiment Analysis</CardTitle>
                <CardDescription>
                  Customer sentiment distribution
                </CardDescription>
              </CardHeader>
              <CardContent>
                {analyticsLoading ? (
                  <ChartSkeleton />
                ) : sentimentData.every((data) => data.value === 0) ? (
                  <EmptyState
                    title="No sentiment data"
                    description="No sentiment analysis data available."
                  />
                ) : (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={sentimentData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) =>
                          `${name}: ${(percent * 100).toFixed(0)}%`
                        }
                        outerRadius={80}
                        stroke="var(--muted)"
                        dataKey="value"
                        fill="#8884d8"
                      >
                        {sentimentData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Hourly Call Distribution</CardTitle>
                <CardDescription>Call volume by hour of day</CardDescription>
              </CardHeader>
              <CardContent>
                {analyticsLoading ? (
                  <ChartSkeleton />
                ) : hourlyCallData.every((data) => data.calls === 0) ? (
                  <EmptyState
                    title="No hourly data"
                    description="No call data available for hourly distribution."
                  />
                ) : (
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={hourlyCallData}>
                      <CartesianGrid
                        strokeDasharray="3 3"
                        stroke="var(--muted)"
                      />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip
                        contentStyle={{
                          borderRadius: 'var(--radius)',
                          backgroundColor: 'var(--card)',
                          color: 'var(--card-foreground)',
                          border: '1px solid var(--muted)',
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="calls"
                        className="fill-blue-500/10 stroke-blue-500"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Call Duration</CardTitle>
                <CardDescription>Distribution of call lengths</CardDescription>
              </CardHeader>
              <CardContent>
                {analyticsLoading ? (
                  <ChartSkeleton />
                ) : callDurationData.every((data) => data.value === 0) ? (
                  <EmptyState
                    title="No duration data"
                    description="No call duration data available."
                  />
                ) : (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={callDurationData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) =>
                          `${name}: ${(percent * 100).toFixed(0)}%`
                        }
                        outerRadius={80}
                        stroke="var(--muted)"
                        dataKey="value"
                      >
                        {callDurationData.map((_, index) => (
                          <Cell
                            key={`cell-${index}`}
                            className={
                              [
                                'fill-emerald-400',
                                'fill-emerald-500',
                                'fill-amber-400',
                                'fill-orange-500',
                                'fill-rose-500',
                              ][index % 5]
                            }
                          />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2"></div>
        </TabsContent>

        <TabsContent value="weekly" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Weekly Call Trends</CardTitle>
              <CardDescription>
                Call volume trends over the past week
              </CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              {analyticsLoading ? (
                <ChartSkeleton height="h-[400px]" />
              ) : weeklyCallData.every((data) => data.calls === 0) ? (
                <EmptyState
                  title="No weekly data"
                  description="No call data available for the past week."
                />
              ) : (
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={weeklyCallData}>
                    <CartesianGrid
                      strokeDasharray="3 3"
                      stroke="var(--muted)"
                    />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip
                      contentStyle={{
                        borderRadius: 'var(--radius)',
                        backgroundColor: 'var(--card)',
                        color: 'var(--card-foreground)',
                        border: '1px solid var(--muted)',
                      }}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="calls"
                      name="Total Calls"
                      stroke="#6366f1"
                      activeDot={{ r: 8 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="successful"
                      name="Successful"
                      stroke="#10b981"
                    />
                    <Line
                      type="monotone"
                      dataKey="unsuccessful"
                      name="Unsuccessful"
                      stroke="#ef4444"
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monthly" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Call Volume</CardTitle>
              <CardDescription>
                Call volume over the past 30 days
              </CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              {analyticsLoading ? (
                <ChartSkeleton height="h-[400px]" />
              ) : monthlyCallData.every((data) => data.calls === 0) ? (
                <EmptyState
                  title="No monthly data"
                  description="No call data available for the past 30 days."
                />
              ) : (
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={monthlyCallData}>
                    <CartesianGrid
                      strokeDasharray="3 3"
                      stroke="var(--muted)"
                    />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip
                      contentStyle={{
                        borderRadius: 'var(--radius)',
                        backgroundColor: 'var(--card)',
                        color: 'var(--card-foreground)',
                        border: '1px solid var(--muted)',
                      }}
                    />
                    <Legend />
                    <Bar
                      dataKey="successful"
                      name="Successful Calls"
                      fill="#10b981"
                      radius={[4, 4, 0, 0]}
                    />
                    <Bar
                      dataKey="unsuccessful"
                      name="Unsuccessful Calls"
                      fill="#ef4444"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Call Status Distribution</CardTitle>
            <CardDescription>
              Breakdown by call completion status
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analyticsLoading ? (
              <ChartSkeleton />
            ) : callStatusData.every((data) => data.value === 0) ? (
              <EmptyState
                title="No status data"
                description="No call status data available."
              />
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={callStatusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) =>
                      `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                    outerRadius={80}
                    stroke="var(--muted)"
                    dataKey="value"
                  >
                    {callStatusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.fill} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Success Rate Trend</CardTitle>
            <CardDescription>Call success rate over time</CardDescription>
          </CardHeader>
          <CardContent>
            {analyticsLoading ? (
              <ChartSkeleton />
            ) : monthlyCallData.every((data) => data.calls === 0) ? (
              <EmptyState
                title="No trend data"
                description="No success rate trend data available."
              />
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <LineChart
                  data={monthlyCallData.map((d) => ({
                    ...d,
                    successRate:
                      d.calls > 0
                        ? Math.round((d.successful / d.calls) * 100)
                        : 0,
                  }))}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="var(--muted)" />
                  <XAxis dataKey="name" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip
                    contentStyle={{
                      borderRadius: 'var(--radius)',
                      backgroundColor: 'var(--card)',
                      color: 'var(--card-foreground)',
                      border: '1px solid var(--muted)',
                    }}
                    formatter={(value) => [`${value}%`, 'Success Rate']}
                  />
                  <Line
                    type="monotone"
                    dataKey="successRate"
                    stroke="#10b981"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Average Duration by Hour</CardTitle>
            <CardDescription>
              Call duration patterns throughout the day
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analyticsLoading ? (
              <ChartSkeleton />
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart
                  data={hourlyCallData.map((data, index) => ({
                    ...data,
                    avgDuration: analyticsData?.calls
                      ? analyticsData.calls
                          .filter(
                            (call) =>
                              new Date(call.start_timestamp).getHours() ===
                              index + 8,
                          )
                          .reduce(
                            (acc, call) => acc + call.duration_ms / 60000,
                            0,
                          ) /
                        Math.max(
                          1,
                          analyticsData.calls.filter(
                            (call) =>
                              new Date(call.start_timestamp).getHours() ===
                              index + 8,
                          ).length,
                        )
                      : 0,
                  }))}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="var(--muted)" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip
                    contentStyle={{
                      borderRadius: 'var(--radius)',
                      backgroundColor: 'var(--card)',
                      color: 'var(--card-foreground)',
                      border: '1px solid var(--muted)',
                    }}
                    formatter={(value) => [
                      `${typeof value === 'number' ? value.toFixed(1) : value} min`,
                      'Avg Duration',
                    ]}
                  />
                  <Area
                    type="monotone"
                    dataKey="avgDuration"
                    stroke="#f59e0b"
                    fill="#f59e0b"
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Call Volume Heatmap</CardTitle>
            <CardDescription>Peak calling hours visualization</CardDescription>
          </CardHeader>
          <CardContent>
            {analyticsLoading ? (
              <ChartSkeleton />
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={hourlyCallData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="var(--muted)" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip
                    contentStyle={{
                      borderRadius: 'var(--radius)',
                      backgroundColor: 'var(--card)',
                      color: 'var(--card-foreground)',
                      border: '1px solid var(--muted)',
                    }}
                  />
                  <Bar dataKey="calls" radius={[4, 4, 0, 0]}>
                    {hourlyCallData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={
                          entry.calls > 10
                            ? '#ef4444'
                            : entry.calls > 5
                              ? '#f59e0b'
                              : entry.calls > 2
                                ? '#10b981'
                                : '#3b82f6'
                        }
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Calls</CardTitle>
          <CardDescription>Your most recent incoming calls</CardDescription>
        </CardHeader>
        <CardContent>
          {analyticsLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-20" />
                </div>
              ))}
            </div>
          ) : recentCalls.length === 0 ? (
            <EmptyState
              title="No recent calls"
              description="No call data available to display."
            />
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Call ID</TableHead>
                  <TableHead>Time</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Sentiment</TableHead>
                  <TableHead>Recording</TableHead>
                  <TableHead>Log</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentCalls.map((call) => (
                  <TableRow key={call.id}>
                    <TableCell className="font-medium">{call.name}</TableCell>
                    <TableCell>{call.time}</TableCell>
                    <TableCell>{call.duration}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {call.status === 'answered' ? (
                          <>
                            <Badge variant="success">
                              <Check className="h-3 w-3 mr-1" /> Successful
                            </Badge>
                          </>
                        ) : (
                          <>
                            <Badge variant="destructive">
                              <PhoneMissed className="h-3 w-3 mr-1" /> Missed
                            </Badge>
                          </>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          call.sentiment.toLowerCase() === 'positive'
                            ? 'success'
                            : call.sentiment.toLowerCase() === 'negative'
                              ? 'destructive'
                              : call.sentiment.toLowerCase() === 'neutral'
                                ? 'secondary'
                                : 'outline'
                        }
                      >
                        {call.sentiment}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {call.recording_url ? (
                        <Button variant="outline" size="sm" asChild>
                          <a
                            href={call.recording_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-1"
                          >
                            <Play className="h-3 w-3" />
                            Play
                          </a>
                        </Button>
                      ) : (
                        <span className="text-muted-foreground">N/A</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {call.public_log_url ? (
                        <Button variant="outline" size="sm" asChild>
                          <a
                            href={call.public_log_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-1"
                          >
                            <ExternalLink className="h-3 w-3" />
                            View
                          </a>
                        </Button>
                      ) : (
                        <span className="text-muted-foreground">N/A</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
