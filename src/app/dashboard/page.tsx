'use client';

import { useState, useEffect, useMemo } from 'react';
import { Building2, Clock, Phone, Plus, TrendingUp } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import {
  ChartSkeleton,
  EmptyState,
  MetricCardSkeleton,
} from '@/components/blocks/analytics-skeletons';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { InviteStaffDialog } from '@/components/blocks/invite-staff-dialog';
import { useClinic } from '@/contexts/ClinicContext';
import { fetchAnalyticsData } from '@/actions/analytics';
import type {
  ApiData,
  Call,
  ProcessedCallData,
  CallTypeData,
  SentimentData,
} from '@/lib/types';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  Legend,
  Tooltip,
} from 'recharts';

import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { useAuth } from '@/contexts/AuthContext';

const callTypeChartConfig = {
  successful: {
    label: 'Successful',
    color: 'hsl(142, 76%, 36%)', // Green
  },
  unsuccessful: {
    label: 'Unsuccessful',
    color: 'hsl(0, 84%, 60%)', // Red
  },
} satisfies ChartConfig;

const callVolumeChartConfig = {
  calls: {
    label: 'Total Calls',
    color: 'hsl(217, 91%, 60%)', // Blue
  },
  successful: {
    label: 'Successful',
    color: 'hsl(142, 76%, 36%)', // Green
  },
  unsuccessful: {
    label: 'Unsuccessful',
    color: 'hsl(0, 84%, 60%)', // Red
  },
} satisfies ChartConfig;

const sentimentChartConfig = {
  positive: {
    label: 'Positive',
    color: 'hsl(142, 76%, 36%)', // Green
  },
  neutral: {
    label: 'Neutral',
    color: 'hsl(45, 93%, 47%)', // Yellow
  },
  negative: {
    label: 'Negative',
    color: 'hsl(0, 84%, 60%)', // Red
  },
} satisfies ChartConfig;

export default function DashboardPage() {
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const { selectedClinic, clinics, loading } = useClinic();
  const { getTokens } = useAuth();
  const [analyticsData, setAnalyticsData] = useState<ApiData | null>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);

  const processCallsData = useMemo(() => {
    return (calls: Call[], period: 'day' | 'week' | 'month') => {
      console.log(`🔍 Processing calls data for period: ${period}`);
      console.log(`📊 Total calls to process: ${calls?.length || 0}`);

      if (!calls || calls.length === 0) {
        console.log('⚠️ No calls data available');
        return [];
      }

      const now = new Date();
      console.log(`⏰ Current time: ${now.toISOString()}`);

      const dataMap = new Map<string, ProcessedCallData>();
      const orderedKeys: string[] = [];

      if (period === 'day') {
        console.log('📅 Processing daily data (last 24 hours)');
        // Last 24 hours, grouped by hour
        for (let i = 23; i >= 0; i--) {
          const hour = new Date(now);
          hour.setHours(now.getHours() - i, 0, 0, 0);
          const key = hour.getHours().toString().padStart(2, '0') + ':00';
          orderedKeys.push(key);
          dataMap.set(key, {
            name: key,
            calls: 0,
            successful: 0,
            unsuccessful: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);

          if (isNaN(callDate.getTime())) {
            console.log(`❌ Invalid date for call: ${call.start_timestamp}`);
            return;
          }

          const diffMs = now.getTime() - callDate.getTime();
          const diffHours = diffMs / (1000 * 60 * 60);

          console.log(
            `📞 Call at ${callDate.toISOString()}, diff: ${diffHours.toFixed(
              2,
            )} hours`,
          );

          if (diffHours >= 0 && diffHours < 24) {
            const hourKey =
              callDate.getHours().toString().padStart(2, '0') + ':00';
            const data = dataMap.get(hourKey);
            if (data) {
              data.calls += 1;
              data.successful += call.call_analysis?.call_successful ? 1 : 0;
              data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
              console.log(`✅ Added call to hour ${hourKey}`);
            }
          }
        });
      } else if (period === 'week') {
        console.log('📅 Processing weekly data (last 7 days)');
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

        for (let i = 6; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(now.getDate() - i);
          date.setHours(0, 0, 0, 0);
          const key = `${date.getMonth() + 1}/${date.getDate()}`;
          orderedKeys.push(key);
          dataMap.set(key, {
            name: days[date.getDay()],
            calls: 0,
            successful: 0,
            unsuccessful: 0,
          });
          console.log(`📆 Created slot for ${key} (${days[date.getDay()]})`);
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);

          if (isNaN(callDate.getTime())) {
            console.log(`❌ Invalid date for call: ${call.start_timestamp}`);
            return;
          }

          const callDateStart = new Date(callDate);
          callDateStart.setHours(0, 0, 0, 0);

          const nowStart = new Date(now);
          nowStart.setHours(0, 0, 0, 0);

          const diffMs = nowStart.getTime() - callDateStart.getTime();
          const diffDays = diffMs / (1000 * 60 * 60 * 24);

          console.log(
            `📞 Call on ${callDate.toISOString()}, diff: ${diffDays.toFixed(2)} days`,
          );

          if (diffDays >= 0 && diffDays < 7) {
            const dateKey = `${callDate.getMonth() + 1}/${callDate.getDate()}`;
            const data = dataMap.get(dateKey);
            if (data) {
              data.calls += 1;
              data.successful += call.call_analysis?.call_successful ? 1 : 0;
              data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
              console.log(`✅ Added call to day ${dateKey}`);
            } else {
              console.log(`❌ No slot found for day ${dateKey}`);
            }
          }
        });
      } else {
        console.log('📅 Processing monthly data (last 30 days)');
        for (let i = 29; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(now.getDate() - i);
          date.setHours(0, 0, 0, 0);
          const key = `${date.getMonth() + 1}/${date.getDate()}`;
          orderedKeys.push(key);
          dataMap.set(key, {
            name: key,
            calls: 0,
            successful: 0,
            unsuccessful: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);

          if (isNaN(callDate.getTime())) {
            console.log(`❌ Invalid date for call: ${call.start_timestamp}`);
            return;
          }

          const callDateStart = new Date(callDate);
          callDateStart.setHours(0, 0, 0, 0);

          const nowStart = new Date(now);
          nowStart.setHours(0, 0, 0, 0);

          const diffMs = nowStart.getTime() - callDateStart.getTime();
          const diffDays = diffMs / (1000 * 60 * 60 * 24);

          console.log(
            `📞 Call on ${callDate.toISOString()}, diff: ${diffDays.toFixed(2)} days`,
          );

          if (diffDays >= 0 && diffDays < 30) {
            const dateKey = `${callDate.getMonth() + 1}/${callDate.getDate()}`;
            const data = dataMap.get(dateKey);
            if (data) {
              data.calls += 1;
              data.successful += call.call_analysis?.call_successful ? 1 : 0;
              data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
              console.log(`✅ Added call to day ${dateKey}`);
            }
          }
        });
      }

      const result = orderedKeys.map((key) => dataMap.get(key)!);
      console.log(`📈 Final processed data for ${period}:`, result);
      return result;
    };
  }, []);

  const processCallTypeData = useMemo(() => {
    return (calls: Call[]): CallTypeData[] => {
      if (!calls || calls.length === 0) {
        return [
          { name: 'Successful', value: 0, fill: 'var(--color-successful)' },
          { name: 'Unsuccessful', value: 0, fill: 'var(--color-unsuccessful)' },
        ];
      }

      const successful = calls.filter(
        (call) => call.call_analysis?.call_successful,
      ).length;
      const unsuccessful = calls.length - successful;

      return [
        {
          name: 'Successful',
          value: successful,
          fill: 'var(--color-successful)',
        },
        {
          name: 'Unsuccessful',
          value: unsuccessful,
          fill: 'var(--color-unsuccessful)',
        },
      ];
    };
  }, []);

  const processSentimentData = useMemo(() => {
    return (calls: Call[]): SentimentData[] => {
      if (!calls || calls.length === 0) return [];

      const sentimentCounts = calls.reduce(
        (acc, call) => {
          const sentiment =
            call.call_analysis?.user_sentiment?.toLowerCase() || 'unknown';
          if (sentiment.includes('positive')) {
            acc.positive += 1;
          } else if (sentiment.includes('negative')) {
            acc.negative += 1;
          } else {
            acc.neutral += 1;
          }
          return acc;
        },
        { positive: 0, neutral: 0, negative: 0 },
      );

      return [
        {
          name: 'Positive',
          value: sentimentCounts.positive,
          fill: 'var(--color-positive)',
        },
        {
          name: 'Neutral',
          value: sentimentCounts.neutral,
          fill: 'var(--color-neutral)',
        },
        {
          name: 'Negative',
          value: sentimentCounts.negative,
          fill: 'var(--color-negative)',
        },
      ].filter((item) => item.value > 0);
    };
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      if (!selectedClinic?._id) return;

      setAnalyticsLoading(true);
      try {
        const { accessToken } = await getTokens();
        if (!accessToken) {
          console.error('No access token available');
          setAnalyticsLoading(false);
          return;
        }
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - 30);

        const response = await fetchAnalyticsData(
          accessToken,
          selectedClinic._id,
          startDate.toISOString().split('T')[0],
          endDate.toISOString().split('T')[0],
        );

        if (response.ok && response.data) {
          setAnalyticsData(response.data);
        } else {
          console.error('Failed to fetch analytics data:', response.error);
        }
      } catch (error) {
        console.error('Error fetching analytics data:', error);
      } finally {
        setAnalyticsLoading(false);
      }
    };

    fetchData();
  }, [selectedClinic?._id, getTokens]);

  const {
    todayCalls,
    weeklyCalls,
    monthlyCallData,
    overallCallTypeData,
    sentimentData,
  } = useMemo(() => {
    const calls = analyticsData?.calls || [];
    const now = new Date();

    console.log('🔄 Processing all call data:', {
      totalCalls: calls.length,
      currentTime: now.toISOString(),
    });

    // Today's calls (last 24 hours)
    const today = calls.filter((call) => {
      const callTime = new Date(call.start_timestamp);

      if (isNaN(callTime.getTime())) {
        console.log(
          `❌ Invalid date for today's call: ${call.start_timestamp}`,
        );
        return false;
      }

      const diffHours = (now.getTime() - callTime.getTime()) / (1000 * 60 * 60);
      const isToday = diffHours >= 0 && diffHours < 24;
      if (isToday) {
        console.log(
          `📅 Today's call: ${callTime.toISOString()}, ${diffHours.toFixed(2)} hours ago`,
        );
      }
      return isToday;
    });

    // Weekly calls (last 7 days)
    const weeklyFilteredCalls = calls.filter((call) => {
      const callTime = new Date(call.start_timestamp);

      if (isNaN(callTime.getTime())) {
        console.log(`❌ Invalid date for weekly call: ${call.start_timestamp}`);
        return false;
      }

      const callDateStart = new Date(callTime);
      callDateStart.setHours(0, 0, 0, 0);

      const nowStart = new Date(now);
      nowStart.setHours(0, 0, 0, 0);

      const diffDays =
        (nowStart.getTime() - callDateStart.getTime()) / (1000 * 60 * 60 * 24);
      const isInWeek = diffDays >= 0 && diffDays < 7;
      if (isInWeek) {
        console.log(
          `📅 Weekly call: ${callTime.toISOString()}, ${diffDays.toFixed(2)} days ago`,
        );
      }
      return isInWeek;
    });

    console.log(
      `📊 Filtered calls - Today: ${today.length}, Weekly: ${weeklyFilteredCalls.length}, All: ${calls.length}`,
    );

    return {
      todayCalls: today,
      weeklyCalls: processCallsData(weeklyFilteredCalls, 'week'),
      monthlyCallData: processCallsData(calls, 'month'),
      overallCallTypeData: processCallTypeData(calls), // For the 30-day pie chart
      sentimentData: processSentimentData(calls),
    };
  }, [
    analyticsData,
    processCallsData,
    processCallTypeData,
    processSentimentData,
  ]);

  const todayCallTypeData = useMemo(
    () => processCallTypeData(todayCalls),
    [todayCalls, processCallTypeData],
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
          {selectedClinic && (
            <p className="text-sm text-muted-foreground mt-1">
              {selectedClinic.clinic_name} • {clinics.length} clinic
              {clinics.length !== 1 ? 's' : ''} total
            </p>
          )}
          {loading && (
            <p className="text-sm text-muted-foreground mt-1">
              Loading clinics...
            </p>
          )}
        </div>
        <div className="mt-2 sm:mt-0">
          <Button onClick={() => setIsInviteDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Invite Staff
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {analyticsLoading ? (
          <>
            <MetricCardSkeleton />
            <MetricCardSkeleton />
            <MetricCardSkeleton />
            <MetricCardSkeleton />
          </>
        ) : (
          <>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Calls
                </CardTitle>
                <Phone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {analyticsData?.calls?.length || 0}
                </div>
                <p className="text-xs text-muted-foreground">Last 30 days</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Avg. Call Duration
                </CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {analyticsData?.summary
                    ? `${analyticsData.summary.average_duration_in_minutes.toFixed(1)}m`
                    : '0m'}
                </div>
                <p className="text-xs text-muted-foreground">Last 30 days</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Successful Calls
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {analyticsData?.calls?.filter(
                    (call) => call.call_analysis?.call_successful,
                  ).length || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  {analyticsData?.calls && analyticsData.calls.length > 0
                    ? `${Math.round(
                        (analyticsData.calls.filter(
                          (call) => call.call_analysis?.call_successful,
                        ).length /
                          analyticsData.calls.length) *
                          100,
                      )}% success rate`
                    : 'No data available'}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Clinics</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{clinics.length}</div>
                <p className="text-xs text-muted-foreground">Active clinics</p>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      {/* New Chart Layout */}
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-7">
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle>Today&apos;s Success Rate (24h)</CardTitle>
            <CardDescription>
              Successful vs. unsuccessful calls in the last 24 hours.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analyticsLoading ? (
              <ChartSkeleton />
            ) : todayCalls.length === 0 ? (
              <EmptyState
                title="No calls today"
                description="No call data available for the last 24 hours."
              />
            ) : (
              <ChartContainer
                config={callTypeChartConfig}
                className="mx-auto max-h-72"
              >
                <PieChart>
                  <ChartTooltip
                    cursor={false}
                    content={<ChartTooltipContent hideLabel />}
                  />
                  <Pie
                    data={todayCallTypeData}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    labelLine={false}
                    label={({ name, percent, value }) =>
                      value > 0 ? `${name} ${(percent * 100).toFixed(0)}%` : ''
                    }
                  >
                    {todayCallTypeData.map((entry) => (
                      <Cell key={entry.name} fill={entry.fill} />
                    ))}
                  </Pie>
                  <Legend />
                </PieChart>
              </ChartContainer>
            )}
          </CardContent>
        </Card>
        <Card className="lg:col-span-4">
          <CardHeader>
            <CardTitle>Weekly Call Analysis</CardTitle>
            <CardDescription>
              Successful and unsuccessful calls over the last 7 days.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analyticsLoading ? (
              <ChartSkeleton height="h-[310px]" />
            ) : weeklyCalls.every((data) => data.calls === 0) ? (
              <EmptyState
                title="No weekly data"
                description="No call data available for the last 7 days."
              />
            ) : (
              <ChartContainer
                config={callVolumeChartConfig}
                className="h-[310px] w-full"
              >
                <BarChart data={weeklyCalls} accessibilityLayer>
                  <CartesianGrid vertical={false} />
                  <XAxis
                    dataKey="name"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                  />
                  <YAxis allowDecimals={false} tickMargin={8} />
                  <Tooltip content={<ChartTooltipContent />} />
                  <Legend />
                  <Bar
                    dataKey="successful"
                    stackId="a"
                    fill="var(--color-successful)"
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar
                    dataKey="unsuccessful"
                    stackId="a"
                    fill="var(--color-unsuccessful)"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ChartContainer>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Monthly Call Trend</CardTitle>
            <CardDescription>
              Volume of calls over the last 30 days.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analyticsLoading ? (
              <ChartSkeleton height="h-[400px]" />
            ) : monthlyCallData.every((data) => data.calls === 0) ? (
              <EmptyState
                title="No monthly data"
                description="No call data available for the last 30 days."
              />
            ) : (
              <ChartContainer
                config={callVolumeChartConfig}
                className="h-[400px] w-full"
              >
                <AreaChart
                  accessibilityLayer
                  data={monthlyCallData}
                  margin={{ left: 12, right: 12, top: 5, bottom: 0 }}
                >
                  <CartesianGrid vertical={false} />
                  <XAxis
                    dataKey="name"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                  />
                  <YAxis allowDecimals={false} tickMargin={8} />
                  <Tooltip content={<ChartTooltipContent indicator="line" />} />
                  <defs>
                    <linearGradient id="fillCalls" x1="0" y1="0" x2="0" y2="1">
                      <stop
                        offset="5%"
                        stopColor="var(--color-calls)"
                        stopOpacity={0.8}
                      />
                      <stop
                        offset="95%"
                        stopColor="var(--color-calls)"
                        stopOpacity={0.1}
                      />
                    </linearGradient>
                  </defs>
                  <Area
                    dataKey="calls"
                    type="natural"
                    fill="url(#fillCalls)"
                    stroke="var(--color-calls)"
                    stackId="a"
                  />
                </AreaChart>
              </ChartContainer>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Call Success Rate (30 Days)</CardTitle>
            <CardDescription>
              Distribution of successful vs unsuccessful calls
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analyticsLoading ? (
              <ChartSkeleton />
            ) : overallCallTypeData.every((data) => data.value === 0) ? (
              <EmptyState
                title="No call data"
                description="No call success data available for the last 30 days."
              />
            ) : (
              <ChartContainer
                config={callTypeChartConfig}
                className="mx-auto max-h-72"
              >
                <PieChart>
                  <ChartTooltip
                    cursor={false}
                    content={<ChartTooltipContent hideLabel />}
                  />
                  <Pie
                    data={overallCallTypeData}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={70}
                    labelLine={false}
                    label={({ name, percent }) =>
                      `${name} ${(percent * 100).toFixed(0)}%`
                    }
                  >
                    {overallCallTypeData.map((entry) => (
                      <Cell key={entry.name} fill={entry.fill} />
                    ))}
                  </Pie>
                  <Legend />
                </PieChart>
              </ChartContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Customer Sentiment (30 Days)</CardTitle>
            <CardDescription>
              Sentiment analysis of customer calls
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analyticsLoading ? (
              <ChartSkeleton />
            ) : sentimentData.length === 0 ? (
              <EmptyState
                title="No sentiment data"
                description="No customer sentiment data available for analysis."
              />
            ) : (
              <ChartContainer
                config={sentimentChartConfig}
                className="mx-auto max-h-72"
              >
                <PieChart>
                  <ChartTooltip
                    cursor={false}
                    content={<ChartTooltipContent hideLabel />}
                  />
                  <Pie
                    data={sentimentData}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={70}
                    labelLine={false}
                    label={({ name, percent }) =>
                      `${name} ${(percent * 100).toFixed(0)}%`
                    }
                  >
                    {sentimentData.map((entry) => (
                      <Cell key={entry.name} fill={entry.fill} />
                    ))}
                  </Pie>
                  <Legend />
                </PieChart>
              </ChartContainer>
            )}
          </CardContent>
        </Card>
      </div>

      <InviteStaffDialog
        open={isInviteDialogOpen}
        onOpenChange={setIsInviteDialogOpen}
        clinics={clinics}
      />
    </div>
  );
}
