'use client';

import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { z } from 'zod';
import { Save, Loader2, AlertCircle, Plus, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { useClinic } from '@/contexts/ClinicContext';
import { updateClinic } from '@/services/clinicService';
import { fetchAuthSession } from 'aws-amplify/auth';

const clinicFormSchema = z.object({
  clinic_name: z.string().min(2, {
    message: 'Clinic name must be at least 2 characters.',
  }),
  clinic_email: z.string().email({
    message: 'Please enter a valid email address.',
  }),
  clinic_website: z
    .string()
    .url({
      message: 'Please enter a valid URL.',
    })
    .optional()
    .or(z.literal('')),
  clinic_phone: z.string().optional(),
  human_transfer_destination_number: z.string().optional(),
  clinic_addresses: z
    .array(
      z.object({
        address: z.string().optional(),
        full_address: z.string().min(1, 'Full address is required'),
        business_location_id: z.string().optional(),
      }),
    )
    .optional(),
});

export default function ClinicProfilePage() {
  const { selectedClinic, setSelectedClinic } = useClinic();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<z.infer<typeof clinicFormSchema>>({
    resolver: zodResolver(clinicFormSchema),
    defaultValues: {
      clinic_name: '',
      clinic_email: '',
      clinic_website: '',
      clinic_phone: '',
      human_transfer_destination_number: '',
      clinic_addresses: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'clinic_addresses',
  });

  // Listen for clinic change events and update form
  useEffect(() => {
    const handleClinicChanged = (event: CustomEvent) => {
      const clinic = event.detail;
      if (clinic) {
        setSelectedClinic(clinic);
        // Clear any existing errors when a new clinic is selected
        setError(null);
      }
    };

    window.addEventListener(
      'clinicChanged',
      handleClinicChanged as EventListener,
    );
    return () => {
      window.removeEventListener(
        'clinicChanged',
        handleClinicChanged as EventListener,
      );
    };
  }, [setSelectedClinic]);

  // Update form when selectedClinic changes from context
  useEffect(() => {
    if (selectedClinic) {
      const addresses =
        selectedClinic.clinic_addresses?.map((addr) => ({
          address: typeof addr === 'string' ? addr : addr.address || '',
          full_address:
            typeof addr === 'string'
              ? addr
              : addr.full_address || addr.address || '',
          business_location_id:
            typeof addr === 'string' ? '' : addr.business_location_id || '',
        })) || [];

      form.reset({
        clinic_name: selectedClinic.clinic_name,
        clinic_email: selectedClinic.clinic_email,
        clinic_website: selectedClinic.clinic_website,
        clinic_phone: selectedClinic.clinic_phone || '',
        human_transfer_destination_number:
          selectedClinic.human_transfer_destination_number || '',
        clinic_addresses: addresses,
      });

      // Clear any errors when clinic data is loaded
      setError(null);
    }
  }, [selectedClinic, form]);

  const onSubmit = async (values: z.infer<typeof clinicFormSchema>) => {
    if (!selectedClinic) {
      toast.error('No clinic selected');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const session = await fetchAuthSession();
      const accessToken = session.tokens?.accessToken?.toString();

      if (!accessToken) {
        toast.error('Authentication required');
        return;
      }

      const result = await updateClinic(
        selectedClinic._id,
        values,
        accessToken,
      );

      if (result.ok) {
        toast.success('Clinic updated successfully');
        if (result.data) {
          setSelectedClinic(result.data);
        }
      } else {
        setError(result.error || 'Failed to update clinic');
        toast.error(result.error || 'Failed to update clinic');
      }
    } catch (error) {
      console.error('Error updating clinic:', error);
      setError('An unexpected error occurred');
      toast.error('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (!selectedClinic) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
          <p className="text-muted-foreground">No clinic selected</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Clinic Profile</h1>
        <p className="text-muted-foreground">
          Manage your clinic information and settings
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Essential clinic details and contact information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="clinic_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Clinic Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter clinic name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clinic_email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter email address" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="clinic_website"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Website</FormLabel>
                      <FormControl>
                        <Input placeholder="https://example.com" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clinic_phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input placeholder="+****************" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="human_transfer_destination_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Human Transfer Number</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter transfer number for call escalations"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Clinic Addresses */}
          <Card>
            <CardHeader>
              <CardTitle>Clinic Addresses</CardTitle>
              <CardDescription>
                Manage your clinic locations and addresses
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {fields.map((field, index) => (
                <div
                  key={field.id}
                  className="space-y-4 p-4 border rounded-lg bg-muted/30"
                >
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium text-sm">Address {index + 1}</h4>
                    {fields.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => remove(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name={`clinic_addresses.${index}.address`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Short Address</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="e.g., Hampton Park"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`clinic_addresses.${index}.business_location_id`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Business Location ID</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Optional location identifier"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name={`clinic_addresses.${index}.full_address`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Address</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., 127-129 Somerville Road Hampton Park, VIC 3976"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={() =>
                  append({
                    address: '',
                    full_address: '',
                    business_location_id: '',
                  })
                }
                className="w-full"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add New Address
              </Button>
            </CardContent>
          </Card>

          {/* System Information */}
          <Card>
            <CardHeader>
              <CardTitle>System Information</CardTitle>
              <CardDescription>
                Read-only system generated information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <FormLabel>Status</FormLabel>
                  <div className="mt-2">
                    <Badge
                      variant={
                        selectedClinic.is_active ? 'success' : 'secondary'
                      }
                    >
                      {selectedClinic.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>

                <div>
                  <FormLabel>Created At</FormLabel>
                  <div className="mt-2">
                    <Input
                      value={new Date(
                        selectedClinic.created_at,
                      ).toLocaleDateString()}
                      disabled
                    />
                  </div>
                </div>

                <div>
                  <FormLabel>Updated At</FormLabel>
                  <div className="mt-2">
                    <Input
                      value={new Date(
                        selectedClinic.updated_at,
                      ).toLocaleDateString()}
                      disabled
                    />
                  </div>
                </div>
              </div>

              <div>
                <FormLabel>Clinic ID</FormLabel>
                <div className="mt-2">
                  <Input
                    value={selectedClinic._id}
                    disabled
                    className="font-mono text-sm"
                  />
                </div>
              </div>

              {selectedClinic.diagnostic_services &&
                selectedClinic.diagnostic_services.length > 0 && (
                  <div>
                    <FormLabel>Diagnostic Services</FormLabel>
                    <div className="mt-2 flex flex-wrap gap-2">
                      {selectedClinic.diagnostic_services.map(
                        (service, index) => (
                          <Badge key={index} variant="outline">
                            {service.name}
                            {service.is_referral_required && (
                              <span className="ml-1 text-xs">
                                (Referral Required)
                              </span>
                            )}
                          </Badge>
                        ),
                      )}
                    </div>
                  </div>
                )}
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={loading} size="lg">
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
