'use client';

import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  ReactNode,
} from 'react';
import { ClinicType } from '@/lib/types';
import { toast } from 'sonner';
import { fetchAuthSession } from 'aws-amplify/auth';
import { getAdminClinics } from '@/actions/admin';

interface AdminClinicContextType {
  clinics: ClinicType[];
  selectedClinic: ClinicType | null;
  loading: boolean;
  error: string | null;
  setSelectedClinic: (clinic: ClinicType) => void;
  fetchClinics: () => Promise<void>;
  refreshClinics: () => Promise<void>;
  clearSelectedClinic: () => void;
}

const AdminClinicContext = createContext<AdminClinicContextType | undefined>(undefined);

interface AdminClinicProviderProps {
  children: ReactNode;
}

export function AdminClinicProvider({ children }: AdminClinicProviderProps) {
  const [clinics, setClinics] = useState<ClinicType[]>([]);
  const [selectedClinic, setSelectedClinicState] = useState<ClinicType | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchClinics = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const session = await fetchAuthSession();
      const accessToken = session.tokens?.accessToken?.toString() || '';
      const idToken = session.tokens?.idToken?.toString() || '';

      if (!accessToken || !idToken) {
        throw new Error('Authentication tokens not found');
      }

      const result = await getAdminClinics(accessToken, idToken);

      if (!result.ok) {
        throw new Error(result.error || 'Failed to fetch clinics');
      }

      const fetchedClinics = result.data?.data || [];
      setClinics(fetchedClinics);

      setSelectedClinicState((currentSelected) => {
        if (fetchedClinics.length > 0 && !currentSelected) {
          localStorage.setItem('adminSelectedClinic', JSON.stringify(fetchedClinics[0]));
          return fetchedClinics[0];
        }
        return currentSelected;
      });
    } catch (error) {
      console.error('Error fetching admin clinics:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch clinics';
      setError(errorMessage);
      toast.error('Failed to load clinics', { description: errorMessage });
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshClinics = useCallback(async () => {
    await fetchClinics();
  }, [fetchClinics]);

  const setSelectedClinic = useCallback((clinic: ClinicType) => {
    setSelectedClinicState(clinic);
    localStorage.setItem('adminSelectedClinic', JSON.stringify(clinic));
    window.dispatchEvent(new CustomEvent('adminClinicChanged', { detail: clinic }));
    console.log(clinic._id);
  }, []);

  const clearSelectedClinic = useCallback(() => {
    setSelectedClinicState(null);
    localStorage.removeItem('adminSelectedClinic');
    window.dispatchEvent(new CustomEvent('adminClinicChanged', { detail: null }));
  }, []);

  useEffect(() => {
    const initializeClinics = async () => {
      const storedClinic = localStorage.getItem('adminSelectedClinic');
      if (storedClinic) {
        try {
          const parsedClinic = JSON.parse(storedClinic);
          setSelectedClinicState(parsedClinic);
        } catch (err) {
          console.error('Error parsing stored clinic:', err);
          localStorage.removeItem('adminSelectedClinic');
        }
      }

      await fetchClinics();
    };

    initializeClinics();
  }, [fetchClinics]);

  return (
    <AdminClinicContext.Provider
      value={{
        clinics,
        selectedClinic,
        loading,
        error,
        setSelectedClinic,
        fetchClinics,
        refreshClinics,
        clearSelectedClinic,
      }}
    >
      {children}
    </AdminClinicContext.Provider>
  );
}

export function useAdminClinic() {
  const context = useContext(AdminClinicContext);
  if (context === undefined) {
    throw new Error('useAdminClinic must be used within an AdminClinicProvider');
  }
  return context;
}
