'use client';

import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  ReactNode,
} from 'react';
import { getUserClinics } from '@/services/clinicService';
import { ClinicType } from '@/lib/types';
import { toast } from 'sonner';
import { useAuth } from './AuthContext';
import { useRouter } from 'next/navigation';

interface ClinicContextType {
  clinics: ClinicType[];
  selectedClinic: ClinicType | null;
  loading: boolean;
  error: string | null;
  setSelectedClinic: (clinic: ClinicType) => void;
  fetchClinics: () => Promise<void>;
  refreshClinics: () => Promise<void>;
  clearSelectedClinic: () => void;
}

const ClinicContext = createContext<ClinicContextType | undefined>(undefined);

interface ClinicProviderProps {
  children: ReactNode;
}

export function ClinicProvider({ children }: ClinicProviderProps) {
  const router = useRouter();
  const [clinics, setClinics] = useState<ClinicType[]>([]);
  const [selectedClinic, setSelectedClinicState] = useState<ClinicType | null>(
    null,
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { getTokens } = useAuth();

  const fetchClinics = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const { accessToken } = await getTokens();

      if (!accessToken) {
        throw new Error('No access token available');
      }

      const result = await getUserClinics(accessToken);

      if (!result.ok) {
        throw new Error(result.error || 'Failed to fetch clinics');
      }

      const fetchedClinics = result.data || [];

      if (fetchedClinics.length === 0) {
        router.push('/onboarding');
        return;
      }

      setClinics(fetchedClinics);

      setSelectedClinicState((currentSelected) => {
        if (fetchedClinics.length > 0 && !currentSelected) {
          localStorage.setItem(
            'selectedClinic',
            JSON.stringify(fetchedClinics[0]),
          );
          return fetchedClinics[0];
        }
        return currentSelected;
      });
    } catch (error) {
      console.error('Error fetching clinics:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to fetch clinics';
      setError(errorMessage);
      toast.error('Failed to load clinics', {
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  }, [getTokens, router]);

  const refreshClinics = useCallback(async () => {
    await fetchClinics();
  }, [fetchClinics]);

  const setSelectedClinic = useCallback((clinic: ClinicType) => {
    console.log('Setting selected clinic:', clinic.clinic_name);
    setSelectedClinicState(clinic);
    localStorage.setItem('selectedClinic', JSON.stringify(clinic));

    // Trigger a custom event to notify other components about clinic change
    window.dispatchEvent(new CustomEvent('clinicChanged', { detail: clinic }));
  }, []);

  const clearSelectedClinic = useCallback(() => {
    console.log('Clearing selected clinic');
    setSelectedClinicState(null);
    localStorage.removeItem('selectedClinic');
    window.dispatchEvent(new CustomEvent('clinicChanged', { detail: null }));
  }, []);

  // Initialize clinic context
  useEffect(() => {
    const initializeClinics = async () => {
      // First fetch clinics
      await fetchClinics();

      // Then check if we need to load from localStorage
      const storedClinic = localStorage.getItem('selectedClinic');
      if (storedClinic) {
        try {
          const parsedClinic = JSON.parse(storedClinic);
          setSelectedClinicState(parsedClinic);
        } catch (error) {
          console.error('Error parsing stored clinic:', error);
          localStorage.removeItem('selectedClinic');
        }
      }
    };

    initializeClinics();
  }, [fetchClinics]);

  return (
    <ClinicContext.Provider
      value={{
        clinics,
        selectedClinic,
        loading,
        error,
        setSelectedClinic,
        fetchClinics,
        refreshClinics,
        clearSelectedClinic,
      }}
    >
      {children}
    </ClinicContext.Provider>
  );
}

export function useClinic() {
  const context = useContext(ClinicContext);
  if (context === undefined) {
    throw new Error('useClinic must be used within a ClinicProvider');
  }
  return context;
}
