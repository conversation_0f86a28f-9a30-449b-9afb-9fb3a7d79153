export interface ClinicType {
  _id: string;
  clinic_name: string;
  clinic_addresses: Array<{
    address?: string;
    full_address?: string;
    business_location_id?: string;
  }>;
  clinic_email: string;
  clinic_website: string;
  clinic_phone?: string;
  is_active: boolean;
  diagnostic_services: Array<{
    name: string;
    is_referral_required: boolean;
  }>;
  created_at: string;
  updated_at: string;
  __v?: number;
  human_transfer_destination_number?: string;
  agent_id?: string;
  crm_details?: {
    name: string;
    auth_details?: Record<string, string>;
    custom_fields?: Record<string, string>;
  };
}

export interface StaffMemberDetails {
  _id: string;
  first_name: string;
  last_name: string;
  email: string;
  uid: string;
  created_at: string;
  updated_at: string;
  __v?: number;
}

export interface StaffMemberMapping {
  _id: string;
  user_id: string;
  clinic_id: string;
  role?: string;
  status?: 'active' | 'pending';
  created_at: string;
  updated_at: string;
  __v?: number;
}

export interface StaffMember {
  user_details: StaffMemberDetails;
  mapping: StaffMemberMapping;
}

export interface StaffMembersResponse {
  data: StaffMember[];
}

export interface KnowledgeBaseType {
  data: {
    _id: string;
    name: string;
    description?: string;
    files?: Array<{
      _id: string;
      filename: string;
      size: number;
      uploadedAt: string;
    }>;
    created_at: string;
    updated_at: string;
    clinic_id: string;
    __v?: number;
  };
}

export interface CreateKnowledgeBaseInput {
  name: string;
  description?: string;
}

export interface UserInviteRequest {
  invitee_email: string;
  invitee_name: string;
  role: 'admin' | 'staff';
  clinic_ids: string[];
}

export interface UserInviteResponse {
  _id: string;
  email: string;
  role: string;
  clinic_id: string;
  invite_token: string;
  status: 'pending' | 'accepted' | 'expired';
  expires_at: string;
  created_at: string;
  updated_at: string;
  first_name?: string;
  last_name?: string;
}

export interface UserInviteDetails {
  data: {
    _id: string;
    invited_by: string;
    inviter_name: string;
    invitee_name: string;
    invitee_email: string;
    clinic_ids: string[];
    role: string;
    token: string;
    accepted: boolean;
    expires_at: string;
    createdAt: string;
    updatedAt: string;
    __v: number;
  };
}

export interface AcceptInviteRequest {
  token: string;
}

export interface AcceptInviteResponse {
  success: boolean;
  message: string;
  user?: {
    _id: string;
    email: string;
    first_name: string;
    last_name: string;
    role: string;
    clinic_id: string;
  };
}

export interface CallAnalysis {
  user_sentiment: string;
  call_successful: boolean;
}

export interface Call {
  _id: string;
  call_id: string;
  agent_id: string;
  call_analysis?: CallAnalysis;
  call_status: string;
  call_type: string;
  created_at: string;
  disconnection_reason: string;
  duration_ms: number;
  end_timestamp: number;
  start_timestamp: number;
  public_log_url: string;
  recording_url: string;
  transcript: string;
}

export interface AnalyticsSummary {
  total_calls: number;
  successful_calls: number;
  unsuccessful_calls: number;
  average_duration_in_minutes: number;
  total_duration_in_minutes: number;
}

export interface ApiData {
  summary: AnalyticsSummary;
  calls: Call[];
}

export interface ProcessedCallData {
  name: string;
  calls: number;
  successful: number;
  unsuccessful: number;
}

export interface CallTypeData {
  name: string;
  value: number;
  fill: string;
}

export interface SentimentData {
  name: string;
  value: number;
  fill: string;
}
