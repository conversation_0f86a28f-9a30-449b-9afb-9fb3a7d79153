{"name": "smart-reception-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "aws-amplify": "^6.15.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "lucide-react": "^0.514.0", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.62"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^20.19.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.28.0", "eslint-config-next": "15.3.2", "husky": "^9.1.7", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}, "lint-staged": {"*.{js,ts}": "eslint --cache --fix", "*.{ts,js,css,md}": "prettier --write"}}